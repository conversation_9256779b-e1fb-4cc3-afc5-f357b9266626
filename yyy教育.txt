yyy教育（http://*************:6966/）
登陆账号：573749877
登陆密码：liuyaxin123

登陆api：
请求 URL
http://*************:6966/api/login
请求方法
POST
状态代码
200 OK
远程地址
127.0.0.1:33210
引用站点策略
strict-origin-when-cross-origin
access-control-allow-headers
Content-Type, Authorization
access-control-allow-methods
GET, POST
access-control-allow-origin
*
cache-control
no-cache
connection
keep-alive
content-encoding
gzip
content-type
application/json; charset=utf-8
date
Sun, 22 Jun 2025 07:17:48 GMT
expires
Sun, 22 Jun 2025 07:17:47 GMT
keep-alive
timeout=4
proxy-connection
keep-alive
server
nginx
transfer-encoding
chunked
vary
Accept-Encoding,User-Agent
x-powered-by
PHP/5.6.40
负载：
{username: "573749877", password: "liuyaxin123"}
password
: 
"liuyaxin123"
username
: 
"573749877"
返回登陆数据：
{code: 200, data: {username: "573749877", nickname: "573749877", roles: ["common"],…}, message: "登录成功"}
code
: 
200
data
: 
{username: "573749877", nickname: "573749877", roles: ["common"],…}
accessToken
: 
"c256668535c8576190496e65871f35b9"
expires
: 
"2030/10/30 00:00:00"
nickname
: 
"573749877"
permissions
: 
["permission:btn:add", "permission:btn:edit"]
0
: 
"permission:btn:add"
1
: 
"permission:btn:edit"
refreshToken
: 
"c256668535c8576190496e65871f35b9"
roles
: 
["common"]
0
: 
"common"
username
: 
"573749877"
message
: 
"登录成功"

公告api：
请求 URL
http://*************:6966/api/console
请求方法
GET
状态代码
200 OK
远程地址
127.0.0.1:33210
引用站点策略
strict-origin-when-cross-origin
access-control-allow-headers
Content-Type, Authorization
access-control-allow-methods
GET, POST
access-control-allow-origin
*
cache-control
no-cache
connection
keep-alive
content-encoding
gzip
content-type
application/json; charset=utf-8
date
Sun, 22 Jun 2025 07:17:49 GMT
expires
Sun, 22 Jun 2025 07:17:48 GMT
keep-alive
timeout=4
proxy-connection
keep-alive
server
nginx
transfer-encoding
chunked
vary
Accept-Encoding,User-Agent
x-powered-by
PHP/5.6.40
accept
application/json, text/plain, */*
accept-encoding
gzip, deflate
accept-language
zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
authorization
Bearer c256668535c8576190496e65871f35b9
cookie
authorized-token={%22accessToken%22:%22c256668535c8576190496e65871f35b9%22%2C%22expires%22:1919520000000%2C%22refreshToken%22:%22c256668535c8576190496e65871f35b9%22}; multiple-tabs=true
host
*************:6966
proxy-connection
keep-alive
referer
http://*************:6966/
user-agent
Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
x-requested-with
XMLHttpRequest
返回：
{code: 200, data: {Balance: 23.1, Run: 6, Stop: 4, Queue: 0, Done: 7,…}, message: "操作成功"}
code
: 
200
data
: 
{Balance: 23.1, Run: 6, Stop: 4, Queue: 0, Done: 7,…}
Balance
: 
23.1
Done
: 
7
Queue
: 
0
Run
: 
6
Stop
: 
4
Timeline
: 
[{timestamp: "2025-06-10",…}, {timestamp: "2025-05-31",…}, {timestamp: "2025-05-27",…}]
0
: 
{timestamp: "2025-06-10",…}
content
: 
"增加 教师专业发展培训网-单考试<br>增加 内蒙古专业技术人员继续教育培训平台<br>增加 中南大学专业技术人员继续教育网-专业科目<br>增加 全国体育传统特色学校体育师资培训平台<br>增加 中国体育人才培训网<br>增加 扎赉特旗专业技术人员继续教育培训网<br>增加 甘肃政法大学继续教育平台<br>增加 新疆医学教育网-国家级继教项目<br>更新  四川一体化 - 形考/终考<br>优化  睿学 - 支持下载资料课件<br>优化  eva系列 - 支持考试/自动获取最新分数<br>优化  山东省教师教育网 - 读取速度<br>增加  好医生APP-收藏课程-常速+不申请证书<br>优化  成都市中小学教师继续教育网 - 自动选课<BR>优化  麦能网对部分新型直播课的支持<br>优化  深圳市教师教育网 - 公需课自动选课<br> 修复一系列问题，已逐一回复，不再赘述"
timestamp
: 
"2025-06-10"
1
: 
{timestamp: "2025-05-31",…}
content
: 
"优化 学习公社云 - 自动选课<br>增加 中南大学专业技术人员继续教育网-公需科目<br>增加 深圳会计进修学院<br>增加 龙岩市专业技术人员继续教育培训平台<br>增加 潍坊职业学院专业技术人员继续教育平台<br>增加 乡村医生线上理论学习培训平台"
timestamp
: 
"2025-05-31"
2
: 
{timestamp: "2025-05-27",…}
content
: 
"优化 广州市中小学教师继续教育网  - 自动提交调查问卷/修复ipx研修宝进度<br>更新 广东省执业药师管理系统 - 因检测改为常速<br>增加 巴中开放大学-专业技术人员继续教育基地 - 考试功能<br>增加 潍坊科技学院继续教育<br>增加 广州新华学院<br>增加 德州市专业技术人员继续教育平台<br>增加 潍坊市人才集团专业技术人员网络培训平台<br>增加  潍城区委党校专业技术人员继续教育网上学习平台<br>增加 广东二师在线-教师培训选学中心（非老二师）"
timestamp
: 
"2025-05-27"
message
: 
"操作成功"

其中Balance是余额


学习项目列表api
请求 URL
http://*************:6966/api/site
请求方法
POST
状态代码
200 OK
远程地址
127.0.0.1:33210
引用站点策略
strict-origin-when-cross-origin
负载：
{version: "20250622004954"}
version
: 
"20250622004954"
返回：
{code: 200, data: {version: "20250622145527", lastoid: 2864, itemCount: 1174, list: [,…],…},…}
code
: 
200
data
: 
{version: "20250622145527", lastoid: 2864, itemCount: 1174, list: [,…],…}
addfee
: 
"0.000"
itemCount
: 
1174
lastoid
: 
2864
list
: 
[,…]
[0 … 99]
0
: 
{id: 1683, name: "合肥永君专业技术人员继续教育在线", trans: "视频+考试 格式：账号 密码 培训名", url: "https://hfyj.zjzx.ah.cn/",…}
id
: 
1683
name
: 
"合肥永君专业技术人员继续教育在线"
price_unit
: 
"2.4 /培训"
trans
: 
"视频+考试 格式：账号 密码 培训名"
url
: 
"https://hfyj.zjzx.ah.cn/"。。。。。以下省略

查课api
请求 URL
http://*************:6966/api/order
请求方法
POST
状态代码
200 OK
远程地址
127.0.0.1:33210
引用站点策略
strict-origin-when-cross-origin
access-control-allow-headers
Content-Type, Authorization
access-control-allow-methods
GET, POST
access-control-allow-origin
*
cache-control
no-cache
connection
keep-alive
content-encoding
gzip
content-type
application/json; charset=utf-8
date
Sun, 10 Aug 2025 13:18:09 GMT
expires
Sun, 10 Aug 2025 13:18:08 GMT
keep-alive
timeout=4
proxy-connection
keep-alive
server
nginx
transfer-encoding
chunked
vary
Accept-Encoding,User-Agent
x-powered-by
PHP/5.6.40
accept
application/json, text/plain, */*
accept-encoding
gzip, deflate
accept-language
zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
authorization
Bearer cb3523165cb6093cbc556e50b5d7b4c4
content-length
77
content-type
application/json
cookie
authorized-token={%22accessToken%22:%22cb3523165cb6093cbc556e50b5d7b4c4%22%2C%22expires%22:1919520000000%2C%22refreshToken%22:%22cb3523165cb6093cbc556e50b5d7b4c4%22}; multiple-tabs=true
host
*************:6966
origin
http://*************:6966
proxy-connection
keep-alive
referer
http://*************:6966/
user-agent
Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
x-requested-with
XMLHttpRequest

负载：{lastoid: "841", orderData: "1394888025 555555", orderNote: "", search: "1"}
lastoid
: 
"841"
orderData
: 
"1394888025 555555"
orderNote
: 
""
search
: 
"1"

返回：
有好几个返回，查课api会重复发送，直到查到，首先会返回：{code: 200, data: [], message: "查询成功"}
code
: 
200
data
: 
[]
message
: 
"查询成功"

查到后会返回：
{code: 200, data: ["1394888025----555555----账号或密码输入出错，需通过“忘记密码”重置。输入出错超过5次之后账号将锁定，您已经1次输入出错，建议您重置密码。"],…}
code
: 
200
data
: 
["1394888025----555555----账号或密码输入出错，需通过“忘记密码”重置。输入出错超过5次之后账号将锁定，您已经1次输入出错，建议您重置密码。"]
0
: 
"1394888025----555555----账号或密码输入出错，需通过“忘记密码”重置。输入出错超过5次之后账号将锁定，您已经1次输入出错，建议您重置密码。"
message
: 
"查询成功"


提交订单api：
请求 URL
http://*************:6966/api/order
请求方法
POST
状态代码
200 OK
远程地址
127.0.0.1:33210
引用站点策略
strict-origin-when-cross-origin
access-control-allow-headers
Content-Type, Authorization
access-control-allow-methods
GET, POST
access-control-allow-origin
*
cache-control
no-cache
connection
keep-alive
content-encoding
gzip
content-length
74
content-type
application/json; charset=utf-8
date
Sun, 10 Aug 2025 13:21:28 GMT
expires
Sun, 10 Aug 2025 13:21:27 GMT
keep-alive
timeout=4
proxy-connection
keep-alive
server
nginx
vary
Accept-Encoding,User-Agent
x-powered-by
PHP/5.6.40
accept
application/json, text/plain, */*
accept-encoding
gzip, deflate
accept-language
zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
authorization
Bearer cb3523165cb6093cbc556e50b5d7b4c4
content-length
145
content-type
application/json
cookie
authorized-token={%22accessToken%22:%22cb3523165cb6093cbc556e50b5d7b4c4%22%2C%22expires%22:1919520000000%2C%22refreshToken%22:%22cb3523165cb6093cbc556e50b5d7b4c4%22}; multiple-tabs=true
host
*************:6966
origin
http://*************:6966
proxy-connection
keep-alive
referer
http://*************:6966/
user-agent
Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
x-requested-with
XMLHttpRequest


负载：
{lastoid: "841", orderData: "18091568961 aA@13970 2025年汉阴县中小学幼儿园教师暑期继续教育培训", orderNote: "",…}
lastoid
: 
"841"
orderData
: 
"18091568961 aA@13970 2025年汉阴县中小学幼儿园教师暑期继续教育培训"
orderNote
: 
""
search
: 
"0"

返回：
{"code":200,"data":{"yid":0},"message":"提交成功"}




订单列表api：
请求 URL
http://*************:6966/api/getorder
请求方法
POST
状态代码
200 OK
远程地址
127.0.0.1:33210
引用站点策略
strict-origin-when-cross-origin
负载：
{lastoid: "", odname: "", nickname: "", notetype: "1", note: "", statusbox: [], page: 1, pageSize: 10}
lastoid
: 
""
nickname
: 
""
note
: 
""
notetype
: 
"1"
odname
: 
""
page
: 
1
pageSize
: 
10
statusbox
: 
[]
返回：
{code: 200, data: {total: 861, current_page: "1", total_pages: 87,…}, message: "操作成功"}
code
: 
200
data
: 
{total: 861, current_page: "1", total_pages: 87,…}
current_page
: 
"1"
list
: 
[{id: 1530800, odname: "25qzjspx4223", odpwd: "ab123123", status: "停止", nickname: "", countc: 1,…},…]
0
: 
{id: 1530800, odname: "25qzjspx4223", odpwd: "ab123123", status: "停止", nickname: "", countc: 1,…}
adddate
: 
"2025-06-22 15:20:11"
charged
: 
"-2"
code
: 
103
countc
: 
1
countm
: 
1
id
: 
1530800
love
: 
0
nickname
: 
""
note
: 
""
odname
: 
"25qzjspx4223"
odpwd
: 
"ab123123"
pid
: 
2864
status
: 
"停止"
studydate
: 
"2025-06-22 15:20:11"
toback
: 
""
train
: 
"2025年全州县中小学(幼儿园)教师全员继续教育网络研修培训项目"。。。。。。以下省略





可学项目api：
请求 URL
http://*************:6966/api/site
请求方法
POST
状态代码
200 OK
远程地址
127.0.0.1:33210
引用站点策略
strict-origin-when-cross-origin
access-control-allow-headers
Content-Type, Authorization
access-control-allow-methods
GET, POST
access-control-allow-origin
*
cache-control
no-cache
connection
keep-alive
content-encoding
gzip
content-type
application/json; charset=utf-8
date
Sun, 10 Aug 2025 13:23:10 GMT
expires
Sun, 10 Aug 2025 13:23:09 GMT
keep-alive
timeout=4
proxy-connection
keep-alive
server
nginx
transfer-encoding
chunked
vary
Accept-Encoding,User-Agent
x-powered-by
PHP/5.6.40
accept
application/json, text/plain, */*
accept-encoding
gzip, deflate
accept-language
zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
authorization
Bearer cb3523165cb6093cbc556e50b5d7b4c4
content-length
14
content-type
application/json
cookie
authorized-token={%22accessToken%22:%22cb3523165cb6093cbc556e50b5d7b4c4%22%2C%22expires%22:1919520000000%2C%22refreshToken%22:%22cb3523165cb6093cbc556e50b5d7b4c4%22}; multiple-tabs=true
host
*************:6966
origin
http://*************:6966
proxy-connection
keep-alive
referer
http://*************:6966/
user-agent
Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
x-requested-with
XMLHttpRequest

负载：
{version: ""}
version
: 
""

会返回本网站所有可以学习的项目的名称，id，价格，说明，网址
{
    "code": 200,
    "data": {
        "version": "20250810211614",
        "lastoid": 841,
        "itemCount": 1365,
        "list": [
            {
                "id": 3501,
                "name": "全国高校教师网络培训中心-人工智能赋能高等教育人才培养",
                "trans": "视频 加速 学完全部项目 &进度有延迟，学完后请过段时间查看 格式：账号 密码",
                "url": "https:\/\/szh.enetedu.com\/site\/personalCenter\/MyCourse",
                "price_unit": "3 \/账号"
            },
            {
                "id": 3138,
                "name": "遂宁开放大学",
                "trans": "视频+考试 格式：账号 密码 培训名",
                "url": "https:\/\/snzyjs.peixunyun.cn\/#\/homepage",
                "price_unit": "3 \/培训"
            },
            {
                "id": 2195,
                "name": "教师专业发展平台",
                "trans": "（如有作业需求可提交到-单独作业专用）视频 格式：账号 密码 培训名",
                "url": "http:\/\/peixun.yanxiuonline.com\/",
                "price_unit": "2 \/培训"

以下省略